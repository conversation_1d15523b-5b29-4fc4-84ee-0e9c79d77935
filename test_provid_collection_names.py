#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试provid集合名称功能

验证修改后的代码能否正确：
1. 根据provid生成集合名称
2. 创建不同provid的MongoDB操作对象
3. 验证集合名称的正确性

作者: User-DF Team
版本: 2.0.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.database.mongodb.connection_pool import MongoDBPool
from shared.database.mongodb.operations import MongoDBOperations
from shared.core import ConfigManager, Logger

def test_collection_name_generation():
    """测试集合名称生成功能"""
    print("=" * 60)
    print("测试集合名称生成功能")
    print("=" * 60)
    
    # 测试连接池的集合名称生成
    pool = MongoDBPool()
    
    # 测试基础集合名称
    base_name = "user_pid_records_optimized"
    
    # 测试不同provid的集合名称生成
    test_cases = [
        (None, "user_pid_records_optimized"),
        (100, "user_pid_records_optimized_100"),
        (200, "user_pid_records_optimized_200"),
        (210, "user_pid_records_optimized_210"),
        (250, "user_pid_records_optimized_250"),
        (531, "user_pid_records_optimized_531"),
        (571, "user_pid_records_optimized_571"),
    ]
    
    print(f"基础集合名称: {base_name}")
    print("\n测试集合名称生成:")
    
    for provid, expected in test_cases:
        result = pool._build_collection_name(base_name, provid)
        status = "✓" if result == expected else "✗"
        provid_str = str(provid) if provid is not None else "None"
        print(f"  provid={provid_str:>4} -> {result:<35} {status}")
        
        if result != expected:
            print(f"    期望: {expected}")
            print(f"    实际: {result}")
    
    print("\n" + "=" * 60)

def test_mongodb_operations():
    """测试MongoDB操作类的provid支持"""
    print("测试MongoDB操作类的provid支持")
    print("=" * 60)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建MongoDB连接池
        pool = MongoDBPool(config_manager=config_manager)
        
        base_collection = "user_pid_records_optimized"
        
        # 测试不同provid的MongoDB操作对象
        test_provids = [100, 200, 210, 250]
        
        print(f"基础集合名称: {base_collection}")
        print("\n创建不同provid的MongoDB操作对象:")
        
        for provid in test_provids:
            try:
                # 创建MongoDB操作对象
                ops = MongoDBOperations(pool, base_collection, provid=provid)
                
                print(f"  provid={provid:>3} -> 集合名称: {ops.collection_name}")
                print(f"           -> 基础名称: {ops.base_collection_name}")
                print(f"           -> provid: {ops.provid}")
                
            except Exception as e:
                print(f"  provid={provid:>3} -> 错误: {e}")
        
        # 测试无provid的情况
        try:
            ops_no_provid = MongoDBOperations(pool, base_collection)
            print(f"  无provid   -> 集合名称: {ops_no_provid.collection_name}")
            print(f"           -> 基础名称: {ops_no_provid.base_collection_name}")
            print(f"           -> provid: {ops_no_provid.provid}")
            
        except Exception as e:
            print(f"  无provid   -> 错误: {e}")
            
    except Exception as e:
        print(f"测试失败: {e}")
    
    print("\n" + "=" * 60)

def test_user_model_changes():
    """测试用户模型的修改"""
    print("测试用户模型的修改")
    print("=" * 60)
    
    try:
        from shared.models import UserModel
        
        # 创建用户模型实例
        user = UserModel(
            uid=12345,
            pid_list=["pid1", "pid2", "pid3"],
            pid_count=3,
            updated_days=19000,
            is_stored=False,
            stored_at_days=None,
            pid_timestamps={"19000": ["pid1", "pid2"], "19001": ["pid3"]}
        )
        
        print("用户模型字段:")
        for field, value in user.__dict__.items():
            print(f"  {field}: {value}")
        
        # 检查是否还有provid字段
        if hasattr(user, 'provid'):
            print("\n⚠️  警告: 用户模型仍然包含provid字段")
        else:
            print("\n✓ 用户模型已成功移除provid字段")
            
    except Exception as e:
        print(f"测试失败: {e}")
    
    print("\n" + "=" * 60)

def main():
    """主测试函数"""
    print("User-DF provid集合名称功能测试")
    print("=" * 60)
    print("测试修改后的代码是否能正确处理provid集合名称")
    print("=" * 60)
    
    try:
        # 初始化日志
        logger = Logger.get_logger("test_provid_collection_names")
        logger.info("开始测试provid集合名称功能")
        
        # 运行测试
        test_collection_name_generation()
        test_mongodb_operations()
        test_user_model_changes()
        
        print("所有测试完成!")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
