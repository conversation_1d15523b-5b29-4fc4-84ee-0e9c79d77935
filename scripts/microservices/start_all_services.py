#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局微服务启动脚本

用于启动所有微服务集群，替代原有的shell脚本
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

class Logger:
    """简单的日志记录器"""
    
    @staticmethod
    def info(message: str):
        print(f"{Colors.GREEN}[INFO]{Colors.NC} {message}")
    
    @staticmethod
    def warn(message: str):
        print(f"{Colors.YELLOW}[WARN]{Colors.NC} {message}")
    
    @staticmethod
    def error(message: str):
        print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")
    
    @staticmethod
    def success(message: str):
        print(f"{Colors.CYAN}[SUCCESS]{Colors.NC} {message}")

def start_orc_mongodb_service(config_file: str, orc_port: int = None, mongodb_port: int = None) -> bool:
    """启动ORC MongoDB服务"""
    logger = Logger()
    
    try:
        # 导入ORC MongoDB服务启动脚本
        from services.orc_mongodb_service.start_services import ServiceManager
        
        logger.info("=== 启动ORC MongoDB服务集群 ===")
        
        # 创建服务管理器
        manager = ServiceManager()
        manager.set_config_file(config_file)
        
        # 设置端口（如果指定）
        if orc_port or mongodb_port:
            manager.set_service_ports(orc_port, mongodb_port)
        
        # 启动服务
        success = manager.start_all_services()
        
        if success:
            logger.success("ORC MongoDB服务集群启动成功")
        else:
            logger.error("ORC MongoDB服务集群启动失败")
        
        return success
        
    except Exception as e:
        logger.error(f"启动ORC MongoDB服务时出错: {e}")
        return False

def stop_orc_mongodb_service() -> bool:
    """停止ORC MongoDB服务"""
    logger = Logger()
    
    try:
        # 导入ORC MongoDB服务启动脚本
        from services.orc_mongodb_service.start_services import ServiceManager
        
        logger.info("=== 停止ORC MongoDB服务集群 ===")
        
        # 创建服务管理器
        manager = ServiceManager()
        
        # 停止服务
        manager.stop_all_services()
        
        logger.success("ORC MongoDB服务集群已停止")
        return True
        
    except Exception as e:
        logger.error(f"停止ORC MongoDB服务时出错: {e}")
        return False

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="全局微服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动所有服务（使用默认配置）
  python3 scripts/microservices/start_all_services.py

  # 使用指定配置文件启动
  python3 scripts/microservices/start_all_services.py --config configs/orc_mongodb_service/production.yaml

  # 指定端口启动
  python3 scripts/microservices/start_all_services.py --orc-port 8001 --mongodb-port 8002

  # 停止所有服务
  python3 scripts/microservices/start_all_services.py --stop

  # 只启动特定服务
  python3 scripts/microservices/start_all_services.py --services orc_mongodb
        """
    )
    
    # 操作参数
    parser.add_argument("--stop", action="store_true", help="停止所有服务")
    
    # 服务选择参数
    parser.add_argument("--services", 
                       choices=["orc_mongodb", "all"], 
                       default="all",
                       help="要启动的服务 (默认: all)")
    
    # 配置参数
    parser.add_argument("--config", 
                       default="configs/orc_mongodb_service/development.yaml",
                       help="配置文件路径")
    
    # 端口参数
    parser.add_argument("--orc-port", type=int, help="ORC处理服务端口")
    parser.add_argument("--mongodb-port", type=int, help="MongoDB写入服务端口")
    
    return parser

def main():
    """主函数"""
    logger = Logger()
    
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()
        
        logger.info("=== 全局微服务管理器 ===")
        
        # 执行操作
        if args.stop:
            # 停止服务
            success = True
            
            if args.services in ["orc_mongodb", "all"]:
                success &= stop_orc_mongodb_service()
            
            if success:
                logger.success("所有服务已停止")
            else:
                logger.error("部分服务停止失败")
                sys.exit(1)
        else:
            # 启动服务
            success = True
            
            if args.services in ["orc_mongodb", "all"]:
                success &= start_orc_mongodb_service(
                    args.config, 
                    args.orc_port, 
                    args.mongodb_port
                )
            
            if success:
                logger.success("所有服务启动成功")
                
                # 显示管理信息
                logger.info("")
                logger.info("服务管理命令:")
                logger.info("  查看状态: python3 services/orc_mongodb_service/status_services.py")
                logger.info("  停止服务: python3 scripts/microservices/start_all_services.py --stop")
                logger.info("  查看tmux会话: tmux list-sessions")
                
            else:
                logger.error("部分服务启动失败")
                sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在退出...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
