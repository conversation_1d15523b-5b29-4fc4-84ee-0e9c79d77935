#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MongoDB provid操作功能

验证修改后的代码能否正确：
1. 在不同provid的集合中写入数据
2. 从不同provid的集合中查询数据
3. 验证数据隔离性

作者: User-DF Team
版本: 2.0.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.database.mongodb.connection_pool import MongoDBPool
from shared.database.mongodb.operations import MongoDBOperations
from shared.core import ConfigManager, Logger
import asyncio

async def test_bulk_upsert_async():
    """测试异步批量upsert功能"""
    print("=" * 60)
    print("测试异步批量upsert功能")
    print("=" * 60)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建MongoDB连接池
        pool = MongoDBPool(config_manager=config_manager)
        
        base_collection = "test_user_records"
        
        # 测试数据
        test_data_100 = [
            {
                "_id": 1001,
                "uid": 1001,
                "pid_groups": [{"timestamp_days": 19000, "pids": ["pid1", "pid2"]}],
                "pid_count": 2,
                "updated_days": 19000,
                "vector_status": {"is_stored": False, "stored_at_days": None}
            },
            {
                "_id": 1002,
                "uid": 1002,
                "pid_groups": [{"timestamp_days": 19000, "pids": ["pid3", "pid4"]}],
                "pid_count": 2,
                "updated_days": 19000,
                "vector_status": {"is_stored": False, "stored_at_days": None}
            }
        ]
        
        test_data_200 = [
            {
                "_id": 2001,
                "uid": 2001,
                "pid_groups": [{"timestamp_days": 19000, "pids": ["pid5", "pid6"]}],
                "pid_count": 2,
                "updated_days": 19000,
                "vector_status": {"is_stored": False, "stored_at_days": None}
            },
            {
                "_id": 2002,
                "uid": 2002,
                "pid_groups": [{"timestamp_days": 19000, "pids": ["pid7", "pid8"]}],
                "pid_count": 2,
                "updated_days": 19000,
                "vector_status": {"is_stored": False, "stored_at_days": None}
            }
        ]
        
        # 测试provid=100的集合
        print("测试provid=100的集合操作:")
        ops_100 = MongoDBOperations(pool, base_collection, provid=100)
        print(f"  集合名称: {ops_100.collection_name}")
        
        try:
            result_100 = await ops_100.bulk_upsert_async(test_data_100)
            print(f"  写入结果: {result_100}")
        except Exception as e:
            print(f"  写入失败: {e}")
        
        # 测试provid=200的集合
        print("\n测试provid=200的集合操作:")
        ops_200 = MongoDBOperations(pool, base_collection, provid=200)
        print(f"  集合名称: {ops_200.collection_name}")
        
        try:
            result_200 = await ops_200.bulk_upsert_async(test_data_200)
            print(f"  写入结果: {result_200}")
        except Exception as e:
            print(f"  写入失败: {e}")
        
        # 验证数据隔离性
        print("\n验证数据隔离性:")
        
        # 查询provid=100的数据
        try:
            count_100 = ops_100.count_documents({})
            print(f"  provid=100集合中的文档数量: {count_100}")
        except Exception as e:
            print(f"  查询provid=100失败: {e}")
        
        # 查询provid=200的数据
        try:
            count_200 = ops_200.count_documents({})
            print(f"  provid=200集合中的文档数量: {count_200}")
        except Exception as e:
            print(f"  查询provid=200失败: {e}")
        
        print("\n✓ 异步批量upsert功能测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)

def test_user_vector_service_config():
    """测试user_vector_service的配置修改"""
    print("测试user_vector_service的配置修改")
    print("=" * 60)
    
    try:
        from services.user_vector_service.mongodb_query import UserDataQuery, UserQueryConfig
        
        # 模拟配置
        config = UserQueryConfig(
            collection_name="user_pid_records_optimized",
            batch_size=100,
            max_pids_per_user=300,
            min_pids_required=3,
            date_range_enabled=False,
            start_date=None,
            end_date=None,
            recent_days=None,
            only_not_stored=True,
            include_expired=True,
            province_enabled=True,
            prov_ids=[100, 200, 210],  # 测试多个省份
            sort_by_update_time=False,
            sort_direction="desc",
            id_range_enabled=False,
            id_range_span=100000,
            start_id=None,
            end_id=None
        )
        
        print("用户查询配置:")
        print(f"  基础集合名称: {config.collection_name}")
        print(f"  启用的省份: {config.prov_ids}")
        print(f"  省份过滤启用: {config.province_enabled}")
        
        # 创建MongoDB连接池
        config_manager = ConfigManager()
        pool = MongoDBPool(config_manager=config_manager)
        
        # 创建模拟的service_config
        class MockServiceConfig:
            def __init__(self):
                self.user_batch_size = 100
                self.min_pids_required = 3

            def get(self, key, default=None):
                return getattr(self, key, default)

        mock_service_config = MockServiceConfig()

        # 创建用户数据查询对象
        query = UserDataQuery(pool, config_manager, mock_service_config)
        
        print(f"\n用户数据查询对象创建成功")
        print(f"  MongoDB操作缓存: {len(query.mongodb_ops_cache)} 个")
        
        # 测试获取不同provid的MongoDB操作对象
        for provid in config.prov_ids:
            ops = query._get_mongodb_ops(provid)
            print(f"  provid={provid} -> 集合名称: {ops.collection_name}")
        
        print("\n✓ user_vector_service配置测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)

async def main():
    """主测试函数"""
    print("User-DF MongoDB provid操作功能测试")
    print("=" * 60)
    print("测试修改后的代码是否能正确处理MongoDB provid操作")
    print("=" * 60)
    
    try:
        # 初始化日志
        logger = Logger.get_logger("test_mongodb_provid_operations")
        logger.info("开始测试MongoDB provid操作功能")
        
        # 运行测试
        await test_bulk_upsert_async()
        test_user_vector_service_config()
        
        print("所有测试完成!")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
