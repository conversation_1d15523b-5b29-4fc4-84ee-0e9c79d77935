#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户向量服务 - 重构版

使用共享模块架构的用户向量计算和存储服务：
- 从MongoDB获取用户历史访问记录
- 从Milvus获取内容向量
- 使用预计算PCA模型进行降维
- 计算用户向量并存储到Milvus
- 单进程批处理模式
- 统一的配置、日志、异常处理

作者: User-DF Team
版本: 2.0.0
"""

import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pymongo import UpdateOne

from shared.core import Config<PERSON>anager, Logger, ExceptionHandler
from shared.database.mongodb import MongoDBPool, MongoDBOperations
from shared.database.mongodb.operations import QueryOptions
from shared.database.milvus import MilvusPool
from shared.utils import DataProcessor, TimeUtils
from services.user_vector_service.processors import VectorProcessor
from services.user_vector_service.mongodb_query import UserDataQuery
from services.user_vector_service.milvus_operations import VectorOperations


@dataclass
class ServiceConfig:
    """服务配置"""
    # 数据过滤配置
    start_date: Optional[str]
    end_date: Optional[str]
    user_limit: Optional[int]

    # 批处理配置
    user_batch_size: int
    vector_batch_size: int

    # 向量计算配置
    min_pids_required: int
    max_pids_for_computation: int
    target_dimension: int
    source_dimension: int

    # PCA配置
    pca_method: str

    # 监控配置
    progress_report_interval: int
    stats_output_interval: int

    # 运行模式
    run_mode: str = "batch"  # batch, continuous


@dataclass
class ProcessingStats:
    """处理统计"""
    total_users: int = 0
    processed_users: int = 0
    valid_users: int = 0
    invalid_users: int = 0
    vectors_computed: int = 0
    vectors_stored: int = 0
    errors: int = 0
    start_time: float = 0.0
    end_time: float = 0.0

    # 进度相关统计
    total_batches: int = 0
    processed_batches: int = 0
    current_batch_start_id: Optional[int] = None
    current_batch_end_id: Optional[int] = None
    min_id: Optional[int] = None
    max_id: Optional[int] = None

    @property
    def processing_time(self) -> float:
        """处理时间"""
        if self.end_time > 0:
            return self.end_time - self.start_time
        return time.time() - self.start_time

    @property
    def users_per_second(self) -> float:
        """每秒处理用户数"""
        if self.processing_time > 0:
            return self.processed_users / self.processing_time
        return 0.0

    @property
    def batch_progress_percentage(self) -> float:
        """批次进度百分比"""
        if self.total_batches > 0:
            return (self.processed_batches / self.total_batches) * 100
        return 0.0

    @property
    def id_progress_percentage(self) -> float:
        """ID范围进度百分比"""
        if (self.min_id is not None and self.max_id is not None and
            self.current_batch_start_id is not None and self.max_id > self.min_id):
            progress = (self.current_batch_start_id - self.min_id) / (self.max_id - self.min_id)
            return min(100.0, max(0.0, progress * 100))
        return 0.0


class UserVectorService:
    """用户向量服务"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化服务
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = Logger.get_logger("UserVectorService")
        
        # 加载配置
        self._load_config()
        
        # 初始化组件
        self.mongodb_pool = MongoDBPool("mongodb", config_manager=self.config_manager)
        self.milvus_pool = MilvusPool("milvus", config_manager=self.config_manager)
        self.data_processor = DataProcessor()
        
        # 初始化业务组件
        self.user_data_query = UserDataQuery(
            self.mongodb_pool, 
            self.config_manager,
            self.config
        )
        self.vector_operations = VectorOperations(
            self.milvus_pool,
            self.config_manager,
            self.config
        )
        self.vector_processor = VectorProcessor(
            self.config_manager,
            self.config
        )
        
        # 统计信息
        self.stats = ProcessingStats()

        # 运行状态
        self.is_running = False

        # 关闭回调函数
        self.shutdown_callback = None

        self.logger.info("用户向量服务初始化完成")

    def set_shutdown_callback(self, callback):
        """
        设置关闭回调函数

        Args:
            callback: 返回是否应该关闭的回调函数
        """
        self.shutdown_callback = callback

        # 同时传递给向量操作对象
        if hasattr(self, 'vector_operations') and self.vector_operations:
            self.vector_operations.set_shutdown_callback(callback)

    def should_shutdown(self) -> bool:
        """
        检查是否应该关闭服务

        Returns:
            是否应该关闭
        """
        if self.shutdown_callback:
            return self.shutdown_callback()
        return False

    def _load_config(self):
        """加载服务配置"""
        try:
            # 加载服务特定配置（包含全局配置项）
            service_config = self.config_manager.get_config("user_vector_service", default={})
            
            # 构建服务配置
            self.config = ServiceConfig(
                # 数据过滤配置
                start_date=service_config.get("start_date"),
                end_date=service_config.get("end_date"),
                user_limit=service_config.get("user_limit"),

                # 批处理配置
                user_batch_size=service_config.get("user_batch_size", 100),
                vector_batch_size=service_config.get("vector_batch_size", 1000),

                # 向量计算配置
                min_pids_required=service_config.get("min_pids_required", 5),
                max_pids_for_computation=service_config.get("max_pids_for_computation", 100),
                target_dimension=service_config.get("target_dimension", 256),
                source_dimension=service_config.get("source_dimension", 512),

                # PCA配置
                pca_method=service_config.get("pca_method", "precomputed_pca"),

                # 监控配置
                progress_report_interval=service_config.get("progress_report_interval", 30),
                stats_output_interval=service_config.get("stats_output_interval", 60),

                # 运行模式
                run_mode=service_config.get("run_mode", "batch")
            )
            
            self.logger.debug(f"服务配置加载完成: {self.config}")
            
        except Exception as e:
            self.logger.error(f"加载服务配置失败: {e}")
            raise
    
    def process_users(self, user_limit: Optional[int] = None) -> bool:
        """
        处理用户向量（按_id范围批次处理）

        Args:
            user_limit: 用户数量限制

        Returns:
            处理是否成功
        """
        try:
            self.logger.info("开始按_id范围批次处理用户向量")

            # 重置统计
            self.stats = ProcessingStats()
            self.stats.start_time = time.time()
            self.is_running = True

            # 获取ID范围边界和计算总批次数
            self._initialize_progress_tracking(user_limit)

            # 使用按_id范围的批次处理
            total_processed = 0
            batch_count = 0

            for user_batch, batch_info in self.user_data_query.get_users_by_id_ranges_with_progress(user_limit):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止用户批次处理")
                    return False

                batch_count += 1
                batch_size = len(user_batch)
                total_processed += batch_size

                # 更新进度信息
                self.stats.processed_batches = batch_count
                self.stats.current_batch_start_id = batch_info.get("start_id")
                self.stats.current_batch_end_id = batch_info.get("end_id")

                # 输出详细进度信息
                self._output_batch_progress(batch_count, batch_size, total_processed, batch_info)

                # 处理当前批次
                batch_success = self.process_user_batch(user_batch)

                if not batch_success:
                    self.stats.errors += 1
                    self.logger.warning(f"第 {batch_count} 批处理失败")

                # 更新统计
                self.stats.total_users = total_processed

                # 定期输出进度
                if batch_count % 10 == 0:
                    self._output_progress_stats()

                # 检查是否达到用户限制
                if user_limit and total_processed >= user_limit:
                    self.logger.info(f"已达到用户限制 {user_limit}，停止处理")
                    break

            # 更新结束时间
            self.stats.end_time = time.time()

            # 输出最终统计
            self.logger.info(f"批次处理完成: 处理了 {batch_count} 个批次，共 {total_processed} 个用户")
            self._output_final_stats()

            return self.stats.errors == 0

        except Exception as e:
            self.logger.error(f"处理用户向量失败: {e}")
            ExceptionHandler.handle_exception(e, {
                "user_limit": user_limit
            })
            return False
        finally:
            self.is_running = False


    def process_user_batch(self, user_batch: List[Dict[str, Any]]) -> bool:
        """
        处理用户批次
        
        Args:
            user_batch: 用户数据批次
            
        Returns:
            处理是否成功
        """
        try:
            self.logger.debug(f"处理用户批次: {len(user_batch)} 个用户")

            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止用户批次处理")
                return False

            # 1. 获取用户的内容向量
            user_content_vectors = self.vector_operations.get_content_vectors_for_users(
                user_batch
            )

            if not user_content_vectors:
                self.logger.debug("批次中没有用户有有效的内容向量")
                return True

            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止向量计算")
                return False

            # 2. 计算用户向量
            user_vectors_to_store = []
            user_provid_map = {}  # 用户ID到provid的映射

            for user_data in user_content_vectors:
                # 在每个用户处理前检查中断信号
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止用户向量计算")
                    return False

                uid = user_data["uid"]
                content_vectors = user_data["content_vectors"]
                timestamps = user_data.get("timestamps", [])
                provid = user_data.get("provid")  # 获取provid信息

                # 计算用户向量
                user_vector = self.vector_processor.compute_user_vector(
                    content_vectors, timestamps
                )

                if user_vector:
                    user_vectors_to_store.append((uid, user_vector))
                    if provid is not None:
                        user_provid_map[uid] = provid
                    self.stats.vectors_computed += 1
                else:
                    self.stats.invalid_users += 1

            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止向量存储")
                return False

            # 3. 存储用户向量到Milvus
            if user_vectors_to_store:
                success = self.vector_operations.store_user_vectors(user_vectors_to_store)
                if success:
                    self.stats.vectors_stored += len(user_vectors_to_store)

                    # 4. 更新MongoDB中的向量状态
                    self._update_vector_status(user_vectors_to_store, user_provid_map)
                else:
                    self.logger.warning(f"批次中 {len(user_vectors_to_store)} 个用户向量存储失败")
                    return False

            self.stats.processed_users += len(user_batch)
            self.stats.valid_users += len(user_vectors_to_store)

            return True

        except Exception as e:
            self.logger.error(f"处理用户批次失败: {e}")
            self.stats.errors += 1
            return False
    

    

    

    
    def _update_vector_status(self, user_vectors: List[Tuple[int, List[float]]], user_provid_map: Dict[int, int] = None):
        """
        更新MongoDB中的向量状态（使用乐观锁）

        Args:
            user_vectors: 用户向量列表
            user_provid_map: 用户ID到provid的映射，如果为None则使用配置中的第一个provid
        """
        try:
            current_days = TimeUtils.date_to_days(TimeUtils.today())
            base_collection_name = self.config_manager.get_config("mongodb", default={}).get("collections", {}).get("user_pid_collection", "user_pid_records_optimized")

            # 按provid分组用户
            users_by_provid = {}
            for uid, vector in user_vectors:
                # 获取用户的provid
                if user_provid_map and uid in user_provid_map:
                    provid = user_provid_map[uid]
                else:
                    # 如果没有提供映射，使用配置中的第一个provid
                    provid = self.user_data_query.query_config.prov_ids[0] if self.user_data_query.query_config.prov_ids else 100

                if provid not in users_by_provid:
                    users_by_provid[provid] = []
                users_by_provid[provid].append((uid, vector))

            # 为每个provid分别处理
            for provid, user_list in users_by_provid.items():
                try:
                    # 为每个provid创建专门的MongoDB操作对象
                    mongodb_ops = MongoDBOperations(
                        self.mongodb_pool,
                        base_collection_name,
                        provid=provid
                    )

                    # 首先获取用户的当前updated_days作为乐观锁
                    user_ids = [uid for uid, _ in user_list]
                    current_users = {}

                    # 批量查询当前用户的updated_days
                    if user_ids:
                        query_options = QueryOptions(projection={"updated_days": 1})
                        users_data = list(mongodb_ops.find_many(
                            {"_id": {"$in": user_ids}},
                            options=query_options
                        ))
                        current_users = {user["_id"]: user.get("updated_days") for user in users_data}

                        # 记录查询结果统计
                        found_count = len(current_users)
                        total_count = len(user_ids)
                        if found_count < total_count:
                            missing_count = total_count - found_count
                            self.logger.debug(f"省份 {provid} 批量查询用户updated_days: 找到 {found_count}/{total_count} 个用户，{missing_count} 个用户不存在")

                    # 批量更新（使用乐观锁）
                    operations = []
                    for uid, _ in user_list:
                        if uid not in current_users:
                            self.logger.warning(f"用户 {uid} (provid: {provid}) 不存在，跳过更新")
                            continue

                        expected_updated_days = current_users[uid]

                        # 使用乐观锁：只有当updated_days没有变化时才更新
                        filter_dict = {
                            "_id": uid,
                            "updated_days": expected_updated_days  # 乐观锁条件
                        }
                        update_dict = {
                            "$set": {
                                "vector_status.is_stored": True,
                                "vector_status.stored_at_days": current_days
                            }
                        }

                        operations.append(UpdateOne(filter_dict, update_dict))

                    if operations:
                        result = mongodb_ops.bulk_write(operations, ordered=False)  # 使用乱序处理
                        if result.success:
                            self.logger.debug(f"省份 {provid} 更新了 {result.modified_count} 个用户的向量状态")
                            if result.modified_count < len(operations):
                                skipped_count = len(operations) - result.modified_count
                                self.logger.debug(f"省份 {provid} 乐观锁冲突跳过 {skipped_count} 个用户")
                        else:
                            self.logger.error(f"省份 {provid} 更新向量状态失败: {result.error_message}")

                except Exception as e:
                    self.logger.error(f"处理省份 {provid} 的向量状态更新失败: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"更新向量状态失败: {e}")
    
    def _initialize_progress_tracking(self, user_limit: Optional[int] = None):
        """
        初始化进度跟踪

        Args:
            user_limit: 用户数量限制（暂未使用，预留用于未来扩展）
        """
        try:
            # 获取ID范围边界
            min_id, max_id = self.user_data_query.get_id_range_bounds()
            self.stats.min_id = min_id
            self.stats.max_id = max_id

            if min_id is not None and max_id is not None:
                # 计算总批次数
                id_range_config = self.config_manager.get_config("user_vector_service", default={}).get("id_range_processing", {})
                span = id_range_config.get("span", 100000)

                # 使用配置的起始和结束ID
                start_id = id_range_config.get("start_id") if id_range_config.get("start_id") is not None else min_id
                end_id = id_range_config.get("end_id") if id_range_config.get("end_id") is not None else max_id + 1

                total_range = end_id - start_id
                self.stats.total_batches = (total_range + span - 1) // span  # 向上取整

                self.logger.info(f"进度跟踪初始化: ID范围 {start_id}-{end_id}, "
                               f"预计 {self.stats.total_batches} 个批次 (每批次 {span} 个ID)")
            else:
                self.logger.warning("无法获取ID范围边界，进度跟踪可能不准确")

        except Exception as e:
            self.logger.error(f"初始化进度跟踪失败: {e}")

    def _output_batch_progress(self, batch_count: int, batch_size: int,
                              total_processed: int, batch_info: Dict[str, Any]):
        """
        输出批次进度信息

        Args:
            batch_count: 批次计数
            batch_size: 当前批次大小
            total_processed: 总处理用户数
            batch_info: 批次信息
        """
        start_id = batch_info.get("start_id", "N/A")
        end_id = batch_info.get("end_id", "N/A")

        # 构建进度信息
        progress_parts = []

        # 批次进度
        if self.stats.total_batches > 0:
            batch_progress = f"批次: {batch_count}/{self.stats.total_batches} ({self.stats.batch_progress_percentage:.1f}%)"
            progress_parts.append(batch_progress)

        # ID范围进度
        if self.stats.min_id is not None and self.stats.max_id is not None:
            id_progress = f"ID进度: {self.stats.id_progress_percentage:.1f}%"
            progress_parts.append(id_progress)

        # 用户数量
        user_info = f"用户: {batch_size}个 (总计: {total_processed})"
        progress_parts.append(user_info)

        # ID范围
        id_range = f"ID范围: {start_id}-{end_id}"
        progress_parts.append(id_range)

        progress_message = " | ".join(progress_parts)
        self.logger.info(f"处理第 {batch_count} 批 - {progress_message}")

    def _output_progress_stats(self):
        """输出进度统计"""
        progress_parts = []

        # 基本进度
        basic_progress = f"用户: {self.stats.processed_users}/{self.stats.total_users}"
        progress_parts.append(basic_progress)

        # 批次进度
        if self.stats.total_batches > 0:
            batch_progress = f"批次: {self.stats.processed_batches}/{self.stats.total_batches} ({self.stats.batch_progress_percentage:.1f}%)"
            progress_parts.append(batch_progress)

        # ID进度
        if self.stats.min_id is not None and self.stats.max_id is not None:
            id_progress = f"ID进度: {self.stats.id_progress_percentage:.1f}%"
            progress_parts.append(id_progress)

        # 向量统计
        vector_stats = f"向量: {self.stats.vectors_computed}计算/{self.stats.vectors_stored}存储"
        progress_parts.append(vector_stats)

        # 处理速度
        speed = f"速度: {self.stats.users_per_second:.0f}用户/s"
        progress_parts.append(speed)

        progress_message = " | ".join(progress_parts)
        self.logger.info(f"进度统计 - {progress_message}")

    def _output_final_stats(self):
        """输出最终统计"""
        final_parts = []

        # 基本统计
        basic_stats = f"用户: {self.stats.processed_users}/{self.stats.total_users}"
        final_parts.append(basic_stats)

        # 批次统计
        if self.stats.total_batches > 0:
            batch_stats = f"批次: {self.stats.processed_batches}/{self.stats.total_batches}"
            final_parts.append(batch_stats)

        # 向量统计
        vector_stats = f"向量: 计算{self.stats.vectors_computed}/存储{self.stats.vectors_stored}"
        final_parts.append(vector_stats)

        # 时间和速度
        time_stats = f"耗时: {self.stats.processing_time:.1f}s"
        final_parts.append(time_stats)

        speed_stats = f"速度: {self.stats.users_per_second:.0f}用户/s"
        final_parts.append(speed_stats)

        # 错误统计
        if self.stats.errors > 0:
            error_stats = f"错误: {self.stats.errors}"
            final_parts.append(error_stats)

        final_message = " | ".join(final_parts)
        self.logger.info(f"处理完成 - {final_message}")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查MongoDB连接
            mongodb_health = self.mongodb_pool.health_check()
            
            # 检查Milvus连接
            milvus_health = self.milvus_pool.health_check()
            
            # 检查PCA模型
            pca_health = self.vector_processor.pca_manager.get_model_status()
            
            return {
                "service": "UserVectorService",
                "status": "healthy",
                "mongodb": mongodb_health,
                "milvus": milvus_health,
                "pca_model": pca_health,
                "stats": {
                    "processed_users": self.stats.processed_users,
                    "vectors_computed": self.stats.vectors_computed,
                    "vectors_stored": self.stats.vectors_stored
                }
            }
            
        except Exception as e:
            return {
                "service": "UserVectorService",
                "status": "unhealthy",
                "error": str(e)
            }
