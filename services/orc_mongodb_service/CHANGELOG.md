# ORC MongoDB服务变更日志

## 版本 2.0.0 - 2025-01-23

### 重大变更

#### 1. 启动方式重构
- **移除**: 原有的shell脚本启动方式 (`scripts/microservices/start_all_services.sh`)
- **新增**: Python启动脚本 (`services/orc_mongodb_service/start_services.py`)
- **新增**: 全局启动脚本 (`scripts/microservices/start_all_services.py`)
- **改进**: 更好的错误处理、跨平台兼容性和服务管理

#### 2. 进程管理升级
- **移除**: nohup后台进程管理
- **新增**: tmux会话管理
- **优势**:
  - 更好的进程控制和监控
  - 实时日志查看
  - 优雅的服务停止和重启

#### 3. 配置文件结构重构
- **移除**: `--environment` 参数
- **移除**: `--config-dir` 参数
- **保留**: `--config` 参数（用于指定完整配置文件路径）
- **新增**: 两层配置文件结构
  - 全局配置文件：包含各微服务配置文件路径
  - 服务特定配置文件：包含具体服务配置参数
- **默认**: 使用 `configs/orc_mongodb_service/development.yaml`

### 新功能

#### 1. 统一启动脚本
```bash
# 启动所有服务
python3 services/orc_mongodb_service/start_services.py

# 使用指定配置
python3 services/orc_mongodb_service/start_services.py --config configs/orc_mongodb_service/production.yaml

# 停止所有服务
python3 services/orc_mongodb_service/start_services.py --stop
```

#### 2. 服务状态监控
```bash
# 检查服务状态
python3 services/orc_mongodb_service/status_services.py
```

#### 3. Tmux会话管理
- ORC处理服务: `orc-mongodb-services-orc`
- MongoDB写入服务: `orc-mongodb-services-mongodb`

### 文件变更

#### 新增文件
- `services/orc_mongodb_service/start_services.py` - ORC MongoDB服务启动脚本
- `services/orc_mongodb_service/status_services.py` - 状态检查脚本
- `services/orc_mongodb_service/test_startup.py` - 启动测试脚本
- `services/orc_mongodb_service/README.md` - 详细使用文档
- `services/orc_mongodb_service/CHANGELOG.md` - 变更日志
- `scripts/microservices/start_all_services.py` - 全局启动脚本
- `scripts/microservices/MIGRATION_NOTICE.md` - 迁移通知

#### 移除文件
- `scripts/microservices/start_all_services.sh`
- `scripts/microservices/status_services.sh`
- `scripts/microservices/stop_all_services.sh`

#### 修改文件
- `services/orc_mongodb_service/orc_processor_service/main.py`
  - 移除 `--environment` 和 `--config-dir` 参数
  - 设置默认配置文件路径
- `services/orc_mongodb_service/mongodb_writer_service/main.py`
  - 移除 `--environment` 和 `--config-dir` 参数
  - 设置默认配置文件路径

### 迁移指南

#### 从旧版本迁移

1. **停止旧服务**（如果正在运行）:
   ```bash
   # 如果使用旧的shell脚本启动的服务还在运行
   pkill -f "orc_processor_service"
   pkill -f "mongodb_writer_service"
   ```

2. **使用新的启动方式**:
   ```bash
   # 使用全局启动脚本（推荐）
   python3 scripts/microservices/start_all_services.py

   # 或直接启动ORC MongoDB服务
   python3 services/orc_mongodb_service/start_services.py
   ```

3. **配置文件调整**:
   - 不再需要指定 `--environment` 参数
   - 直接使用 `--config` 指定完整的配置文件路径
   - 配置文件现在使用两层结构（全局配置 + 服务特定配置）

#### 常用命令对照

| 旧命令 | 新命令 |
|--------|--------|
| `./scripts/microservices/start_all_services.sh` | `python3 scripts/microservices/start_all_services.py` |
| `./scripts/microservices/status_services.sh` | `python3 services/orc_mongodb_service/status_services.py` |
| `./scripts/microservices/stop_all_services.sh` | `python3 scripts/microservices/start_all_services.py --stop` |

### 技术改进

#### 1. 错误处理
- 更详细的错误信息和日志
- 优雅的异常处理和恢复机制
- 环境检查和依赖验证

#### 2. 服务管理
- 自动的tmux会话管理
- 健康检查和状态监控
- 优雅的服务启动和停止

#### 3. 配置管理
- 简化的配置参数
- 更清晰的配置文件结构
- 更好的默认值设置

### 测试

#### 运行测试
```bash
# 测试启动脚本功能（不实际启动服务）
python3 services/orc_mongodb_service/test_startup.py
```

#### 测试覆盖
- 环境检查（Python、tmux、配置文件）
- 服务配置验证
- 模块导入测试
- 会话管理测试

### 兼容性

- **Python**: 需要 Python 3.7+
- **Tmux**: 需要 tmux 2.0+
- **操作系统**: Linux, macOS
- **配置文件**: 向后兼容现有配置文件格式

### 已知问题

无已知问题。

### 下一步计划

1. 监控生产环境中的新启动方式表现
2. 根据用户反馈优化服务管理功能
3. 考虑添加更多的监控和诊断工具
