#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务启动脚本

使用tmux启动所有相关的微服务，替代原有的shell脚本启动方式
"""

import os
import sys
import time
import argparse
import subprocess
import signal
from pathlib import Path
from typing import List, Dict, Optional
import requests
import yaml

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

class Logger:
    """简单的日志记录器"""
    
    @staticmethod
    def info(message: str):
        print(f"{Colors.GREEN}[INFO]{Colors.NC} {message}")
    
    @staticmethod
    def warn(message: str):
        print(f"{Colors.YELLOW}[WARN]{Colors.NC} {message}")
    
    @staticmethod
    def error(message: str):
        print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")
    
    @staticmethod
    def debug(message: str):
        print(f"{Colors.BLUE}[DEBUG]{Colors.NC} {message}")
    
    @staticmethod
    def success(message: str):
        print(f"{Colors.CYAN}[SUCCESS]{Colors.NC} {message}")

class ServiceConfig:
    """服务配置类"""
    
    def __init__(self, name: str, module_path: str, default_port: int, tmux_session: str):
        self.name = name
        self.module_path = module_path
        self.default_port = default_port
        self.tmux_session = tmux_session
        self.port = default_port
        self.host = "0.0.0.0"

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.logger = Logger()
        self.project_root = PROJECT_ROOT
        self.services: Dict[str, ServiceConfig] = {}
        self.config_file = None
        self.tmux_base_session = "orc-mongodb-services"
        
        # 定义服务配置
        self._init_services()

    def _init_services(self):
        """初始化服务配置"""
        self.services = {
            "orc_processor_service": ServiceConfig(
                name="ORC处理服务",
                module_path="services.orc_mongodb_service.orc_processor_service.main",
                default_port=8001,
                tmux_session=f"{self.tmux_base_session}-orc"
            ),
            "mongodb_writer_service": ServiceConfig(
                name="MongoDB写入服务",
                module_path="services.orc_mongodb_service.mongodb_writer_service.main",
                default_port=8002,
                tmux_session=f"{self.tmux_base_session}-mongodb"
            )
        }
    
    def check_python_environment(self) -> bool:
        """检查Python环境"""
        try:
            result = subprocess.run([sys.executable, "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info(f"Python版本: {result.stdout.strip()}")
                return True
            else:
                self.logger.error("Python环境检查失败")
                return False
        except Exception as e:
            self.logger.error(f"Python环境检查异常: {e}")
            return False
    
    def check_tmux_available(self) -> bool:
        """检查tmux是否可用"""
        try:
            result = subprocess.run(["tmux", "-V"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info(f"Tmux版本: {result.stdout.strip()}")
                return True
            else:
                self.logger.error("Tmux未安装或不可用")
                return False
        except Exception as e:
            self.logger.error(f"Tmux检查异常: {e}")
            return False
    
    def check_config_file(self, config_file: str) -> bool:
        """检查配置文件是否存在"""
        config_path = self.project_root / config_file
        if config_path.exists():
            self.logger.info(f"全局配置文件: {config_path}")
            return True
        else:
            self.logger.error(f"全局配置文件不存在: {config_path}")
            return False

    def load_global_config(self, config_file: str) -> Dict:
        """加载全局配置文件"""
        try:
            config_path = self.project_root / config_file
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            return config
        except Exception as e:
            self.logger.error(f"加载全局配置文件失败: {e}")
            return {}

    def get_service_config_file(self, global_config: Dict, service_key: str) -> Optional[str]:
        """从全局配置中获取服务的配置文件路径"""
        try:
            services_config = global_config.get("services", {})
            service_config = services_config.get(service_key, {})
            config_file = service_config.get("config_file")

            if config_file:
                # 检查服务配置文件是否存在
                service_config_path = self.project_root / config_file
                if service_config_path.exists():
                    self.logger.info(f"{service_key} 配置文件: {service_config_path}")
                    return config_file
                else:
                    self.logger.error(f"{service_key} 配置文件不存在: {service_config_path}")
                    return None
            else:
                self.logger.error(f"全局配置中未找到 {service_key} 的配置文件路径")
                return None

        except Exception as e:
            self.logger.error(f"获取 {service_key} 配置文件路径失败: {e}")
            return None
    
    def kill_existing_sessions(self):
        """杀死现有的tmux会话"""
        self.logger.info("清理现有的tmux会话...")
        
        for service_key, service in self.services.items():
            try:
                # 检查会话是否存在
                result = subprocess.run(
                    ["tmux", "has-session", "-t", service.tmux_session],
                    capture_output=True
                )
                
                if result.returncode == 0:
                    # 会话存在，杀死它
                    subprocess.run(
                        ["tmux", "kill-session", "-t", service.tmux_session],
                        capture_output=True
                    )
                    self.logger.info(f"已杀死现有会话: {service.tmux_session}")
                    
            except Exception as e:
                self.logger.debug(f"清理会话 {service.tmux_session} 时出错: {e}")
    
    def start_service_in_tmux(self, service_key: str, service: ServiceConfig, service_config_file: str) -> bool:
        """在tmux中启动服务"""
        try:
            # 构建启动命令
            cmd_parts = [
                sys.executable, "-m", service.module_path,
                "--host", service.host,
                "--port", str(service.port)
            ]

            # 使用服务特定的配置文件
            if service_config_file:
                cmd_parts.extend(["--config", service_config_file])

            cmd_str = " ".join(cmd_parts)

            self.logger.info(f"启动 {service.name}...")
            self.logger.debug(f"命令: {cmd_str}")
            self.logger.debug(f"使用配置文件: {service_config_file}")

            # 创建tmux会话并运行命令
            tmux_cmd = [
                "tmux", "new-session", "-d", "-s", service.tmux_session,
                "-c", str(self.project_root),
                cmd_str
            ]

            result = subprocess.run(tmux_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.success(f"{service.name} 已在tmux会话 '{service.tmux_session}' 中启动")
                return True
            else:
                self.logger.error(f"{service.name} 启动失败: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"启动 {service.name} 时出错: {e}")
            return False
    
    def check_service_health(self, service: ServiceConfig, max_retries: int = 10) -> bool:
        """检查服务健康状态"""
        self.logger.info(f"检查 {service.name} 健康状态...")
        
        for retry in range(max_retries):
            try:
                response = requests.get(
                    f"http://{service.host}:{service.port}/health",
                    timeout=5
                )
                if response.status_code == 200:
                    self.logger.success(f"{service.name} 健康检查通过")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            if retry < max_retries - 1:
                self.logger.debug(f"等待 {service.name} 启动... ({retry + 1}/{max_retries})")
                time.sleep(2)
        
        self.logger.error(f"{service.name} 健康检查失败")
        return False
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        self.logger.info("=== 启动ORC MongoDB服务集群 ===")

        # 环境检查
        if not self.check_python_environment():
            return False

        if not self.check_tmux_available():
            return False

        if self.config_file and not self.check_config_file(self.config_file):
            return False

        # 加载全局配置
        global_config = {}
        if self.config_file:
            global_config = self.load_global_config(self.config_file)
            if not global_config:
                self.logger.error("无法加载全局配置文件")
                return False

        # 清理现有会话
        self.kill_existing_sessions()

        # 启动服务
        failed_services = []

        for service_key, service in self.services.items():
            # 获取服务特定的配置文件路径
            service_config_file = None
            if global_config:
                service_config_file = self.get_service_config_file(global_config, service_key)
                if not service_config_file:
                    self.logger.error(f"无法获取 {service.name} 的配置文件")
                    failed_services.append(service.name)
                    continue

            if not self.start_service_in_tmux(service_key, service, service_config_file):
                failed_services.append(service.name)

        if failed_services:
            self.logger.error(f"以下服务启动失败: {', '.join(failed_services)}")
            return False

        # 等待服务启动
        self.logger.info("等待服务完全启动...")
        time.sleep(5)

        # 健康检查
        self.logger.info("执行健康检查...")
        health_failed = []

        for service_key, service in self.services.items():
            if not self.check_service_health(service):
                health_failed.append(service.name)

        if health_failed:
            self.logger.error(f"以下服务健康检查失败: {', '.join(health_failed)}")
            return False

        # 显示服务状态
        self.show_service_status()
        return True
    
    def show_service_status(self):
        """显示服务状态"""
        self.logger.info("=== 服务启动完成 ===")
        
        for service_key, service in self.services.items():
            self.logger.info(f"{service.name}: http://{service.host}:{service.port}")
            self.logger.info(f"  Tmux会话: {service.tmux_session}")
        
        self.logger.info("")
        self.logger.info("服务管理命令:")
        self.logger.info("  查看所有tmux会话: tmux list-sessions")
        self.logger.info("  连接到服务会话: tmux attach-session -t <session-name>")
        self.logger.info("  停止所有服务: python3 services/orc_mongodb_service/start_services.py --stop")
        self.logger.info("")
    
    def stop_all_services(self):
        """停止所有服务"""
        self.logger.info("=== 停止ORC MongoDB服务集群 ===")
        
        for service_key, service in self.services.items():
            try:
                result = subprocess.run(
                    ["tmux", "has-session", "-t", service.tmux_session],
                    capture_output=True
                )
                
                if result.returncode == 0:
                    subprocess.run(
                        ["tmux", "kill-session", "-t", service.tmux_session],
                        capture_output=True
                    )
                    self.logger.success(f"已停止 {service.name} (会话: {service.tmux_session})")
                else:
                    self.logger.info(f"{service.name} 未运行")
                    
            except Exception as e:
                self.logger.error(f"停止 {service.name} 时出错: {e}")
        
        self.logger.info("所有服务已停止")
    
    def set_config_file(self, config_file: str):
        """设置配置文件"""
        self.config_file = config_file
    
    def set_service_ports(self, orc_port: Optional[int] = None, mongodb_port: Optional[int] = None):
        """设置服务端口"""
        if orc_port:
            self.services["orc_processor_service"].port = orc_port
        if mongodb_port:
            self.services["mongodb_writer_service"].port = mongodb_port

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="ORC MongoDB服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动所有服务（使用默认配置）
  python3 services/orc_mongodb_service/start_services.py

  # 使用指定配置文件启动
  python3 services/orc_mongodb_service/start_services.py --config configs/orc_mongodb_service/production.yaml

  # 指定端口启动
  python3 services/orc_mongodb_service/start_services.py --orc-port 8001 --mongodb-port 8002

  # 停止所有服务
  python3 services/orc_mongodb_service/start_services.py --stop
        """
    )
    
    # 操作参数
    parser.add_argument("--stop", action="store_true", help="停止所有服务")
    
    # 配置参数
    parser.add_argument("--config", 
                       default="configs/orc_mongodb_service/development.yaml",
                       help="配置文件路径")
    
    # 端口参数
    parser.add_argument("--orc-port", type=int, help="ORC处理服务端口")
    parser.add_argument("--mongodb-port", type=int, help="MongoDB写入服务端口")
    
    return parser

def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()
        
        # 创建服务管理器
        manager = ServiceManager()
        
        # 设置配置文件
        manager.set_config_file(args.config)
        
        # 设置端口
        manager.set_service_ports(args.orc_port, args.mongodb_port)
        
        # 执行操作
        if args.stop:
            manager.stop_all_services()
        else:
            success = manager.start_all_services()
            if not success:
                sys.exit(1)
        
    except KeyboardInterrupt:
        Logger.info("接收到中断信号，正在退出...")
        sys.exit(0)
    except Exception as e:
        Logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
