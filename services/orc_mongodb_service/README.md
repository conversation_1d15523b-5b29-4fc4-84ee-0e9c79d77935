# ORC MongoDB服务

ORC MongoDB服务是一个微服务架构的数据处理系统，负责处理ORC文件数据并写入MongoDB数据库。

## 服务架构

该服务包含以下微服务：

1. **ORC处理服务** (`orc_processor_service`) - 端口 8001
   - 负责读取和处理ORC文件
   - 提供数据处理API接口

2. **MongoDB写入服务** (`mongodb_writer_service`) - 端口 8002
   - 负责从消息队列接收数据并批量写入MongoDB
   - 提供写入状态监控API

## 新的启动方式

### 使用Python启动脚本（推荐）

我们已经将原有的shell脚本启动方式替换为Python启动脚本，并使用tmux替代nohup进行服务管理。

#### 使用全局启动脚本（推荐）

```bash
# 使用默认配置启动所有服务
python3 scripts/microservices/start_all_services.py

# 使用指定配置文件启动
python3 scripts/microservices/start_all_services.py --config configs/orc_mongodb_service/production.yaml

# 指定端口启动
python3 scripts/microservices/start_all_services.py --orc-port 8001 --mongodb-port 8002

# 只启动ORC MongoDB服务
python3 scripts/microservices/start_all_services.py --services orc_mongodb
```

#### 直接启动ORC MongoDB服务

```bash
# 使用默认配置启动
python3 services/orc_mongodb_service/start_services.py

# 使用指定配置文件启动
python3 services/orc_mongodb_service/start_services.py --config configs/orc_mongodb_service/production.yaml

# 指定端口启动
python3 services/orc_mongodb_service/start_services.py --orc-port 8001 --mongodb-port 8002
```

#### 停止所有服务

```bash
# 使用全局启动脚本停止（推荐）
python3 scripts/microservices/start_all_services.py --stop

# 直接停止ORC MongoDB服务
python3 services/orc_mongodb_service/start_services.py --stop
```

#### 查看服务状态

```bash
python3 services/orc_mongodb_service/status_services.py
```

### 配置文件结构

服务现在使用两层配置文件结构：

#### 全局配置文件
- 开发环境：`configs/orc_mongodb_service/development.yaml`
- 生产环境：`configs/orc_mongodb_service/production.yaml`

全局配置文件只包含各个微服务的配置文件路径，例如：
```yaml
# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "2.0.0"
  environment: "development"

# ==================== 微服务配置文件路径 ====================
services:
  orc_processor_service:
    config_file: "configs/orc_mongodb_service/orc_processor_service/development.yaml"
  mongodb_writer_service:
    config_file: "configs/orc_mongodb_service/mongodb_writer_service/development.yaml"
```

#### 服务特定配置文件
- ORC处理服务：`configs/orc_mongodb_service/orc_processor_service/development.yaml`
- MongoDB写入服务：`configs/orc_mongodb_service/mongodb_writer_service/development.yaml`

这些文件包含各个服务的具体配置参数。

### Tmux会话管理

服务使用tmux进行管理，每个服务运行在独立的tmux会话中：

- ORC处理服务：`orc-mongodb-services-orc`
- MongoDB写入服务：`orc-mongodb-services-mongodb`

#### 常用tmux命令

```bash
# 查看所有tmux会话
tmux list-sessions

# 连接到ORC处理服务会话
tmux attach-session -t orc-mongodb-services-orc

# 连接到MongoDB写入服务会话
tmux attach-session -t orc-mongodb-services-mongodb

# 从tmux会话中分离（不停止服务）
# 在tmux会话中按 Ctrl+B，然后按 D

# 杀死特定会话
tmux kill-session -t orc-mongodb-services-orc
```

## 单独启动服务

如果需要单独启动某个服务，可以直接运行对应的main.py文件：

### ORC处理服务

```bash
python3 -m services.orc_mongodb_service.orc_processor_service.main \
    --host 0.0.0.0 \
    --port 8001 \
    --config configs/orc_mongodb_service/development.yaml
```

### MongoDB写入服务

```bash
python3 -m services.orc_mongodb_service.mongodb_writer_service.main \
    --host 0.0.0.0 \
    --port 8002 \
    --config configs/orc_mongodb_service/development.yaml
```

## API接口

### 健康检查

所有服务都提供健康检查接口：

```bash
# ORC处理服务
curl http://localhost:8001/health

# MongoDB写入服务
curl http://localhost:8002/health
```

### 统计信息

```bash
# MongoDB写入服务统计
curl http://localhost:8002/stats

# 队列状态
curl http://localhost:8002/queue/status
```

## 日志查看

### 通过tmux查看实时日志

连接到对应的tmux会话即可查看实时日志输出。

### 服务日志文件

日志文件位置根据配置文件中的设置确定，通常在：
- `logs/orc_processor_service/`
- `logs/mongodb_writer_service/`

## 故障排除

### 服务启动失败

1. 检查配置文件是否存在且格式正确
2. 检查端口是否被占用
3. 检查MongoDB和Redis连接是否正常
4. 查看tmux会话中的错误日志

### 服务无响应

1. 使用状态检查脚本检查服务状态
2. 连接到tmux会话查看日志
3. 检查系统资源使用情况

### 重启服务

```bash
# 停止所有服务
python3 services/orc_mongodb_service/start_services.py --stop

# 等待几秒钟

# 重新启动服务
python3 services/orc_mongodb_service/start_services.py
```

## 配置说明

### 主要配置项

- `mongodb`: MongoDB连接和操作配置
- `milvus`: Milvus向量数据库配置
- `redis`: Redis消息队列配置
- `batch_processing`: 批处理相关配置
- `logging`: 日志配置

### 环境特定配置

不同环境使用不同的配置文件：
- 开发环境：较小的批处理大小，本地数据库连接
- 生产环境：优化的批处理大小，生产数据库连接

## 迁移说明

### 从旧的shell脚本迁移

1. 停止使用 `scripts/microservices/start_all_services.sh`
2. 使用新的Python启动脚本 `services/orc_mongodb_service/start_services.py`
3. 配置文件路径保持不变，但不再使用 `--environment` 参数
4. 使用tmux替代nohup进行服务管理

### 主要变化

1. **启动方式**：从shell脚本改为Python脚本
2. **进程管理**：从nohup改为tmux
3. **配置方式**：移除 `--environment` 参数，只使用 `--config`
4. **服务管理**：提供更好的服务状态监控和管理功能
