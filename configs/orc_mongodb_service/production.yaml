# ORC MongoDB服务 - 生产环境全局配置文件
# 版本: 2.0.0
# 包含各个微服务的配置文件路径

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "2.0.0"
  environment: "production"

# ==================== 微服务配置文件路径 ====================
services:
  # ORC处理服务配置文件路径
  orc_processor_service:
    config_file: "configs/orc_mongodb_service/orc_processor_service/production.yaml"

  # MongoDB写入服务配置文件路径
  mongodb_writer_service:
    config_file: "configs/orc_mongodb_service/mongodb_writer_service/development.yaml"

  # 监控服务配置文件路径（如果需要）
  monitoring_service:
    config_file: "configs/orc_mongodb_service/monitoring_service/development.yaml"